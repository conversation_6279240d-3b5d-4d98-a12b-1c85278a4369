import { TargetEntity, TargetType } from "@ghq-abi/northstar-domain";

export function filterTargetsByType(
  targetsList: TargetEntity[],
  targetType: TargetType,
): TargetEntity[] {
  return targetsList
    .filter(target =>
      target.targetTypes?.some(
        targetTypeItem => targetTypeItem.type === targetType,
      ),
    )
    .map(target => ({
      ...target,
      children:
        target.children?.filter(child =>
          child.targetTypes?.some(
            targetTypeItem => targetTypeItem.type === targetType,
          ),
        ) || [],
    }));
}