import {
  ProposalRepository,
  ProposalEntity,
  ProposalStatus,
  FindProposalsFilters,
  TargetRepository,
  TargetTypeRepository,
  TargetEntity,
  TargetTypeEntity,
  TargetType,
} from '@ghq-abi/northstar-domain';
import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateTargetsOnProposalDto } from './dtos/create-targets-on-proposal.dto';
import { filterTargetsByType } from '../../helpers/filterTargetsByType.helper';

@Injectable()
export class ProposalService {
  constructor(
    private readonly proposalRepository: ProposalRepository,
    private readonly targetRepository: TargetRepository,
    private readonly targetTypeRepository: TargetTypeRepository,
  ) {}

  public async findAllProposals(
    filters: FindProposalsFilters,
    pageNumber: number,
    pageSize: number,
  ): Promise<{
    data: ProposalEntity[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
  }> {
    return this.proposalRepository.findAllProposals(
      filters,
      pageNumber,
      pageSize,
    );
  }

  public async createTargetsOnProposal(
    uidProposal: string,
    createTargetsOnProposalDto: CreateTargetsOnProposalDto,
  ): Promise<ProposalEntity> {
    if (createTargetsOnProposalDto.targets[0].children) {
      const mergeTargets = await this.mergeTargets(
        uidProposal,
        createTargetsOnProposalDto,
      );

      if (mergeTargets) {
        return mergeTargets;
      }
    }
    await Promise.all(
      createTargetsOnProposalDto.targets.map(async (target) => {
        return this.createSingleTarget(target, uidProposal);
      }),
    );

    const proposal = await this.proposalRepository.findByUid(uidProposal);

    if (proposal.status === ProposalStatus.NOT_STARTED) {
      return await this.proposalRepository.updateProposal(uidProposal, {
        status: ProposalStatus.IN_PROGRESS_PROPOSAL,
      });
    }

    return proposal;
  }

  public async findProposalByUid(uid: string): Promise<ProposalEntity | null> {
    return this.proposalRepository.findByUid(uid);
  }

  public async updateProposal(
    uid: string,
    proposalData: Partial<ProposalEntity>,
  ): Promise<ProposalEntity> {
    const proposal = await this.proposalRepository.findByUid(uid);
    let totalWeight: number;

    if (proposal.targets.length) {
      switch (proposalData.status) {
        // Proposal targets types are changing from Proposal to Feedback
        case ProposalStatus.IN_PROGRESS_FEEDBACK:
          const proposalTargets = filterTargetsByType(
            proposal.targets,
            TargetType.PROPOSAL,
          );
          totalWeight = proposalTargets.reduce(
            (sum, target) => sum + (target.weight || 0),
            0,
          );
          break;

        // Proposal targets types are changing from Feedback to Final
        case ProposalStatus.IN_PROGRESS_FINAL:
          const feedbackTargets = filterTargetsByType(
            proposal.targets,
            TargetType.FEEDBACK,
          );
          totalWeight = feedbackTargets.reduce(
            (sum, target) => sum + (target.weight || 0),
            0,
          );
          break;

        // Proposal targets types are changing from Final to Completed
        case ProposalStatus.COMPLETED:
          const finalTargets = filterTargetsByType(
            proposal.targets,
            TargetType.FINAL,
          );
          totalWeight = finalTargets.reduce(
            (sum, target) => sum + (target.weight || 0),
            0,
          );
          break;
      }

      if (totalWeight !== 100) {
        throw new BadRequestException(
          `Sum of weight of all targets should be 100`,
        );
      }

      return this.proposalRepository.updateProposal(uid, proposalData);
    } else {
      throw new BadRequestException(
        `Proposal should have targets attached to it.`,
      );
    }
  }

  public async deleteProposal(uid: string): Promise<void> {
    return this.proposalRepository.deleteProposal(uid);
  }

  public async deleteTargetsFromProposal(
    uidProposal: string,
    targets: {
      uid: string;
      targetType: TargetType;
    }[],
  ): Promise<ProposalEntity> {
    const selectedTargets = await this.targetRepository.findByUids(
      targets.map((target) => target.uid),
    );

    await Promise.all(
      selectedTargets.map(async (target) => {
        const targetTypeToDelete = targets.find(
          (t) => t.uid === target.uid,
        )?.targetType;

        if (targetTypeToDelete) {
          await this.processTargetDeletion(target, targetTypeToDelete);
        }
      }),
    );

    return this.proposalRepository.findByUid(uidProposal);
  }

  public async getFilters(): Promise<{
    data: {
      status: { label: string; value: string }[];
      zones: { label: string; value: string }[];
      functions: { label: string; value: string }[];
      sltLevels: { label: string; value: string }[];
      sltNames: { label: string; value: string }[];
    };
  }> {
    return this.proposalRepository.getFilters();
  }

  public async updateTargetsFromProposal(
    uidProposal: string,
    updateTargetsOnProposalDto: CreateTargetsOnProposalDto,
  ): Promise<ProposalEntity> {
    await Promise.all(
      updateTargetsOnProposalDto.targets.map(async (target) => {
        const updatedTarget = await this.targetRepository.updateTarget({
          uid: target.uid,
          scope: target.scope,
          weight: target.weight,
        } as unknown as TargetEntity);

        if (target.children && target.children.length > 0) {
          await Promise.all(
            target.children.map(async (child) =>
              this.targetRepository.updateTarget({
                uid: child.uid,
                scope: child.scope,
                weight: child.weight,
              } as unknown as TargetEntity),
            ),
          );

          const totalChildrenWeight = target.children.reduce(
            (sum, child) => sum + (child.weight || 0),
            0,
          );

          if (totalChildrenWeight !== target.weight) {
            await this.targetRepository.updateTarget({
              uid: target.uid,
              weight: totalChildrenWeight ?? target.weight,
            } as unknown as TargetEntity);
          }
        }

        return updatedTarget;
      }),
    );

    return this.proposalRepository.findByUid(uidProposal);
  }

  private async createSingleTarget(
    target: any,
    uidProposal: string,
  ): Promise<TargetEntity> {
    const parentWeight = this.calculateParentWeight(target);

    const createdTarget = await this.createParentTarget(
      target,
      uidProposal,
      parentWeight,
    );

    if (target.children && target.children.length > 0) {
      await this.processChildrenTargets(target.children, createdTarget.uid);
    }

    if (target.targetType) {
      await this.createTargetType(createdTarget.uid, target.targetType);
    }

    return createdTarget;
  }

  private calculateParentWeight(target: any): number {
    if (target.children && target.children.length > 0) {
      return target.children.reduce(
        (sum, child) => sum + (child.weight || 0),
        0,
      );
    }
    return target.weight;
  }

  private async createParentTarget(
    target: any,
    uidProposal: string,
    weight: number,
  ): Promise<TargetEntity> {
    return this.targetRepository.createTarget({
      scope: target.scope,
      weight: weight ?? target.weight,
      uidProposal: uidProposal,
      uidDeliverable: target.uidDeliverable,
    } as unknown as TargetEntity);
  }

  private async processChildrenTargets(
    children: any[],
    parentTargetUid: string,
  ): Promise<void> {
    const childrenToCreate = children.filter((child) => !child.uid);
    const childrenToUpdate = children.filter((child) => child.uid);

    if (childrenToCreate.length > 0) {
      await this.createChildrenTargets(childrenToCreate, parentTargetUid);
    }

    if (childrenToUpdate.length > 0) {
      await this.updateChildrenTargets(childrenToUpdate, parentTargetUid);
    }
  }

  private async createChildrenTargets(
    childrenToCreate: any[],
    parentTargetUid: string,
  ): Promise<void> {
    const createdChildren = await this.targetRepository.createTargets(
      childrenToCreate.map(
        (child) =>
          ({
            ...child,
            uidParentTarget: parentTargetUid,
          }) as unknown as TargetEntity,
      ),
    );

    await this.createTargetTypesForChildren(createdChildren, childrenToCreate);
  }

  private async updateChildrenTargets(
    childrenToUpdate: any[],
    parentTargetUid: string,
  ): Promise<void> {
    const updatedChildren = await this.targetRepository.createTargets(
      childrenToUpdate.map(
        (child) =>
          ({
            ...child,
            uidParentTarget: parentTargetUid,
          }) as unknown as TargetEntity,
      ),
    );

    await this.createTargetTypesForChildren(updatedChildren, childrenToUpdate);
  }

  private async createTargetTypesForChildren(
    children: TargetEntity[],
    originalChildren: any[],
  ): Promise<void> {
    await Promise.all(
      children.map(async (child, index) => {
        const childTargetType = originalChildren[index].targetType;
        if (childTargetType) {
          await this.createTargetType(child.uid, childTargetType);
        }
      }),
    );
  }

  private async createTargetType(
    targetUid: string,
    targetType: TargetType,
  ): Promise<void> {
    await this.targetTypeRepository.create({
      uidTarget: targetUid,
      type: targetType,
    } as unknown as TargetTypeEntity);
  }

  private async synchronizeParentTargetTypesWithChildren(
    parentTarget: TargetEntity,
  ) {
    const parentTargetTypes =
      parentTarget?.targetTypes?.map((tt) => tt.type) || [];

    const childrenTargetTypes =
      parentTarget.children?.flatMap(
        (child) => child.targetTypes?.map((tt) => tt.type) || [],
      ) || [];

    if (parentTargetTypes !== childrenTargetTypes) {
      const targetTypesWhoExistsInChildrenButNotInParent = [
        ...new Set(
          childrenTargetTypes.filter(
            (type) => !parentTargetTypes.includes(type),
          ),
        ),
      ];

      await Promise.all(
        targetTypesWhoExistsInChildrenButNotInParent.map(async (targetType) => {
          parentTarget.children.map(async (child) => {
            await this.targetTypeRepository.deleteTargetTypesByTargetUidAndType(
              child.uid,
              targetType,
            );
          });
        }),
      );
    }
  }

  private async mergeTargets(
    uidProposal: string,
    createTargetsOnProposalDto: CreateTargetsOnProposalDto,
  ): Promise<ProposalEntity | void> {
    const targets = await this.targetRepository.findByUids(
      createTargetsOnProposalDto.targets[0].children.map((t) => t.uid),
    );

    if (
      !(
        targets.some((t) => t.uidParentTarget) &&
        targets.some((t) => !t.uidParentTarget)
      )
    ) {
      return;
    }

    const targetsWithoutParent = targets.find((t) => !t.uidParentTarget);

    await this.targetRepository.updateTarget({
      uid: targetsWithoutParent.uid,
      uidParentTarget: targets.find((t) => t.uidParentTarget).uidParentTarget,
    } as unknown as TargetEntity);

    return this.proposalRepository.findByUid(uidProposal);
  }

  private verifyIfTheTargetTypesFromATargetIsMoreThanOne(target: TargetEntity) {
    return target.targetTypes && target.targetTypes.length > 1;
  }

  private async processTargetDeletion(
    target: TargetEntity,
    targetTypeToDelete: TargetType,
  ): Promise<void> {
    if (this.verifyIfTheTargetTypesFromATargetIsMoreThanOne(target)) {
      await this.targetTypeRepository.deleteTargetTypesByTargetUidAndType(
        target.uid,
        targetTypeToDelete,
      );

      const updatedTarget = await this.targetRepository.findByUid(target.uid);
      await this.synchronizeParentTargetTypesWithChildren(updatedTarget);
    } else {
      await this.targetRepository.deleteTargetsByUids([target.uid]);
    }

    await this.handleParentTargetAfterDeletion(target.uidParentTarget);
  }

  private async handleParentTargetAfterDeletion(
    parentTargetUid: string,
  ): Promise<void> {
    if (!parentTargetUid) return;

    const parentTarget = await this.targetRepository.findByUid(parentTargetUid);

    if (
      parentTarget &&
      parentTarget.children &&
      parentTarget.children.length === 0 &&
      parentTarget.uidDeliverable === null &&
      parentTarget.uidParentTarget === null
    ) {
      await this.targetRepository.deleteTargetsByUids([parentTarget.uid]);
    } else if (parentTarget) {
      await this.targetRepository.updateParentTargetWeight(parentTarget);
    }
  }
}
