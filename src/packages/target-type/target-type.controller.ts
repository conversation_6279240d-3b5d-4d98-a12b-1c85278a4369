import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { TargetTypeService } from './target-type.service';
import { TargetType } from '@ghq-abi/northstar-domain';

@ApiTags('Target Type')
@Controller('target-type')
export class TargetTypeController {
  constructor(private readonly targetTypeService: TargetTypeService) {}

  @Post('feedback')
  public async createFeedback(
    @Body() body: { targetUids: string[]; agree?: boolean },
  ) {
    return this.targetTypeService.createTargetType(
      body.targetUids,
      TargetType.FEEDBACK,
      body.agree,
    );
  }

  @Post('final')
  public async createFinal(
    @Body() body: { targetUids: string[]; agree?: boolean },
  ) {
    return this.targetTypeService.createTargetType(
      body.targetUids,
      TargetType.FINAL,
      body.agree,
    );
  }
}
