import {
  TargetRepository,
  TargetType,
  TargetTypeEntity,
  TargetTypeRepository,
} from '@ghq-abi/northstar-domain';
import { Injectable } from '@nestjs/common';

@Injectable()
export class TargetTypeService {
  constructor(
    private readonly targetTypeRepository: TargetTypeRepository,
    private readonly targetRepository: TargetRepository,
  ) {}

  public async createTargetType(
    uidTargets: string[],
    type: TargetType,
    agree?: boolean,
  ): Promise<TargetTypeEntity[] | null> {
    const existingTargetTypes =
      await this.verifyIfTargetTypesExists(uidTargets);

    const createdEntities = await Promise.all(
      uidTargets.map(async (uidTarget) => {
        const existingTargetType = existingTargetTypes.find(
          (targetType) =>
            targetType.uidTarget === uidTarget && targetType.type === type,
        );

        if (existingTargetType) {
          return existingTargetType;
        }

        const newTargetType = Object.assign(new TargetTypeEntity(), {
          uidTarget,
          type,
          ...((type === TargetType.FINAL || type === TargetType.FEEDBACK) &&
            agree !== undefined && { agree }),
        });

        const createdTargetType =
          await this.targetTypeRepository.createTargetType(newTargetType);

        await this.createTargetTypesForChildren(uidTarget, type, agree);

        return createdTargetType;
      }),
    );

    return createdEntities;
  }

  private async verifyIfTargetTypesExists(
    uidTargets: string[],
  ): Promise<TargetTypeEntity[]> {
    return this.targetTypeRepository.findTargetTypesByTargetUids(uidTargets);
  }

  private async createTargetTypesForChildren(
    parentTargetUid: string,
    type: TargetType,
    agree?: boolean,
  ): Promise<void> {
    const parentTarget = await this.targetRepository.findByUid(parentTargetUid);

    if (
      parentTarget &&
      parentTarget.children &&
      parentTarget.children.length > 0
    ) {
      const childrenUids = parentTarget.children.map((child) => child.uid);

      const existingChildrenTargetTypes =
        await this.targetTypeRepository.findTargetTypesByTargetUids(
          childrenUids,
        );

      const childrenToCreateTargetType = childrenUids.filter((childUid) => {
        return !existingChildrenTargetTypes.some(
          (targetType) =>
            targetType.uidTarget === childUid && targetType.type === type,
        );
      });

      await Promise.all(
        childrenToCreateTargetType.map(async (childUid) => {
          const newChildTargetType = Object.assign(new TargetTypeEntity(), {
            uidTarget: childUid,
            type,
            ...((type === TargetType.FINAL || type === TargetType.FEEDBACK) &&
              agree !== undefined && { agree }),
          });

          return this.targetTypeRepository.createTargetType(newChildTargetType);
        }),
      );
    }
  }
}
